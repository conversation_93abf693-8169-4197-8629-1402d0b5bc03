package test

import (
	"meta/app/ent"
	"meta/app/ent/matrixspectrumalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var matrixSpectrumAlertApi = baseApi + "/matrixspectrumalert"
var remark30 = "XRNoXhuSDe"
var testMatrixSpectrumAlert = &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark30, IP: "XQkKAkonMm", DeviceName: "PiJxjJwTOX", Interface: "XFcAwFBpaL", ProtectType: "tDlusMJdQK", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "HBkrewdOxv", Bps: 376}
var matrixSpectrumAlertIDs = []int{}

// Create MatrixSpectrumAlert test case
// 创建
func TestCreateMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "POST", BodyData: testMatrixSpectrumAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	matrixSpectrumAlertIDs = append(matrixSpectrumAlertIDs, getDataMapId(result))
}

// CreateBulk MatrixSpectrumAlert test case
// 批量创建
func TestCreateBulkMatrixSpectrumAlert(t *testing.T) {
	remark1 := "RQJQsPdXjX"
	bulkData1 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, IP: "bLOMQSJNsm", DeviceName: "woFsRLbiMM", Interface: "RtUPfvuKoU", ProtectType: "lHbpyeVbmm", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "YtFUmgdcpr", Bps: 348}
	remark2 := "AXjYwqSCKq"
	bulkData2 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, IP: "goEgRRyfQX", DeviceName: "TgqlgQIwGG", Interface: "cCxVnHjVyp", ProtectType: "OAAGJKanEn", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "lmWtTNEBdZ", Bps: 338}
	remark3 := "JaVKBPnqJK"
	bulkData3 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, IP: "dcwRTdtlOy", DeviceName: "EGfoORjhOk", Interface: "rrBstWtkIi", ProtectType: "LwiPEmVosH", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "jOKJclfxqd", Bps: 342}
	remark4 := "wTTrfTwiGQ"
	bulkData4 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, IP: "XurLDrAcWC", DeviceName: "RAYdiEzRNf", Interface: "lXGjjnlbpp", ProtectType: "AVbgtHcdCv", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "jXQOYYSwQZ", Bps: 340}
	remark5 := "PGgiLLlGSu"
	bulkData5 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, IP: "lwnRZCJCRB", DeviceName: "TViDXUWCaU", Interface: "OrghgyRLcU", ProtectType: "EECCWVKIVg", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "OHfhJRUsHY", Bps: 371}
	bulkDatas := [...]ent.MatrixSpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		matrixSpectrumAlertIDs = append(matrixSpectrumAlertIDs, getDataMapId(v))
	}
}

// Query MatrixSpectrumAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumAlert test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumAlert not exist test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query MatrixSpectrumAlert by IP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumAlertByIP(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: matrixspectrumalert.FieldIP + "=" + testMatrixSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch MatrixSpectrumAlert search by IP test case
// 分页搜索
func TestQuerySearchMatrixSpectrumAlertIP(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "search=" + testMatrixSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID MatrixSpectrumAlert test case
// 根据 ID 修改
func TestUpdateByIDMatrixSpectrumAlert(t *testing.T) {
	remark30 := "PURkETZtTp"
	updateData := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark30, IP: "OPhlpwbtKH", DeviceName: "eOWHANZsHr", Interface: "DicaDijQrs", ProtectType: "gVXUvlYHyM", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "ZMoTYOhfwc", Bps: 332}
	successExpectedResult.ResponseData = updateData.IP
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumAlert test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk MatrixSpectrumAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkMatrixSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: matrixSpectrumAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
