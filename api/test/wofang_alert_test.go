package test

import (
	"meta/app/ent"
	"meta/app/ent/wofangalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var wofangAlertApi = baseApi + "/wofangalert"
var remark14 = "xuhhaRSvFB"
var testWofangAlert = &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, AttackStatus: "SZthKVCfFy", AttackType: "OXpeRsbFdR", DeviceIP: "zryknlUUsZ", ZoneIP: "MZVUJrarbN", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 355, MaxInKbps: 370}
var wofangAlertIDs = []int{}

// Create WofangAlert test case
// 创建
func TestCreateWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "POST", BodyData: testWofangAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	wofangAlertIDs = append(wofangAlertIDs, getDataMapId(result))
}

// CreateBulk WofangAlert test case
// 批量创建
func TestCreateBulkWofangAlert(t *testing.T) {
	remark1 := "zeFUdZekGo"
	bulkData1 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, AttackStatus: "STdfSPmYxP", AttackType: "KfPmQnqxCK", DeviceIP: "uGQPZyOhrR", ZoneIP: "eTxXehFZPR", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 367, MaxInKbps: 350}
	remark2 := "LftfYafTns"
	bulkData2 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, AttackStatus: "ahuXoTPHzg", AttackType: "VXAUhQYeuI", DeviceIP: "iwNyHRQiuc", ZoneIP: "jxBKIBBzLP", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 322, MaxInKbps: 361}
	remark3 := "zbMucHGpQt"
	bulkData3 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, AttackStatus: "nlliCtSaXI", AttackType: "KGDSBNUgRZ", DeviceIP: "ypYweEdRrb", ZoneIP: "RIpAdTtnWv", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 393, MaxInKbps: 392}
	remark4 := "DSmSvLIqKy"
	bulkData4 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, AttackStatus: "GCnUKXkYpb", AttackType: "OkHvtEmQPn", DeviceIP: "UKPZiQZUlX", ZoneIP: "QJGvHRWeLU", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 376, MaxInKbps: 392}
	remark5 := "HlOvOvnFjz"
	bulkData5 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, AttackStatus: "kldofccnPp", AttackType: "MaGFEEoGYc", DeviceIP: "WNDpBteqxe", ZoneIP: "VOBIKRAwam", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 316, MaxInKbps: 379}
	bulkDatas := [...]ent.WofangAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		wofangAlertIDs = append(wofangAlertIDs, getDataMapId(v))
	}
}

// Query WofangAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID WofangAlert test case
// 根据 ID 查询
func TestQueryByIDWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID WofangAlert not exist test case
// 根据 ID 查询
func TestQueryByIDWofangAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query WofangAlert by AttackStatus test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlertByAttackStatus(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: wofangalert.FieldAttackStatus + "=" + testWofangAlert.AttackStatus, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch WofangAlert search by AttackStatus test case
// 分页搜索
func TestQuerySearchWofangAlertAttackStatus(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "search=" + testWofangAlert.AttackStatus, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID WofangAlert test case
// 根据 ID 修改
func TestUpdateByIDWofangAlert(t *testing.T) {
	remark14 := "KQmNnKqnam"
	updateData := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, AttackStatus: "HcxIyVBtNE", AttackType: "csRvrVcrKj", DeviceIP: "IYcaqLtEEj", ZoneIP: "kFBEUHJatq", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropKbps: 384, MaxInKbps: 339}
	successExpectedResult.ResponseData = updateData.AttackStatus
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID WofangAlert test case
// 根据 ID 删除
func TestDeleteByIDWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID WofangAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDWofangAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk WofangAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: wofangAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
