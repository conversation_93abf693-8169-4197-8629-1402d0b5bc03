package test

import (
	"meta/app/ent"
	"meta/app/ent/datasync"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var dataSyncApi = baseApi + "/datasync"
var testDataSync = &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "iGVljFAyUb", Type: "TLsvdoFQLw", Region: "iSzqigumLe", Source: "BjPGEcmQHr"}
var dataSyncIDs = []int{}

// Create DataSync test case
// 创建
func TestCreateDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "POST", BodyData: testDataSync, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataSyncIDs = append(dataSyncIDs, getDataMapId(result))
}

// CreateBulk DataSync test case
// 批量创建
func TestCreateBulkDataSync(t *testing.T) {
	bulkData1 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "QpaLbhaIFl", Type: "ZRFjjawFHP", Region: "NpONPzibXG", Source: "pMQGiqoJZB"}
	bulkData2 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "dYUHeAxEzE", Type: "RUoTzYCRnN", Region: "WMrEqvdUij", Source: "csACoTqOJm"}
	bulkData3 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "OUjksIdGQV", Type: "JJbGoCABvG", Region: "riMaTsGbwX", Source: "BlDHFkglaT"}
	bulkData4 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "FgCYeajPRg", Type: "QnlAWDYrDN", Region: "wDURYcEvKp", Source: "kFdVOCZVxO"}
	bulkData5 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "PMQlwXmCyx", Type: "ZZvkoRJZyD", Region: "urrjmeZqdk", Source: "NYMplOmdqZ"}
	bulkDatas := [...]ent.DataSync{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.NetType
	testCase := &CaseRule{Api: dataSyncApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		dataSyncIDs = append(dataSyncIDs, getDataMapId(v))
	}
}

// Query DataSync test case
// 根据指定字段、时间范围查询或搜索
func TestQueryDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID DataSync test case
// 根据 ID 查询
func TestQueryByIDDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: strconv.Itoa(dataSyncIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID DataSync not exist test case
// 根据 ID 查询
func TestQueryByIDDataSyncNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query DataSync by NetType test case
// 根据指定字段、时间范围查询或搜索
func TestQueryDataSyncByNetType(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: datasync.FieldNetType + "=" + testDataSync.NetType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch DataSync search by NetType test case
// 分页搜索
func TestQuerySearchDataSyncNetType(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "search=" + testDataSync.NetType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID DataSync test case
// 根据 ID 修改
func TestUpdateByIDDataSync(t *testing.T) {
	updateData := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), NetType: "ofZhxUpArg", Type: "UZDXmwPzWH", Region: "LtTZAKgPUZ", Source: "mHrCboLBpi"}
	successExpectedResult.ResponseData = updateData.NetType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "PUT", UrlData: strconv.Itoa(dataSyncIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID DataSync test case
// 根据 ID 删除
func TestDeleteByIDDataSync(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(dataSyncIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID DataSync not exist test case
// 根据 ID 删除
func TestDeleteByIDDataSyncNoExist(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk DataSync test case
// 根据 IDs 批量删除
func TestDeleteBulkDataSync(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: dataSyncIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
