package test

import (
	"meta/app/ent"
	"meta/app/ent/socgroupticket"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var socGroupTicketApi = baseApi + "/socgroupticket"
var remark12 = "lQhMXPJwfm"
var testSocGroupTicket = &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Name: "UxVDbkGlEc", Type: "HjiTZapAJu", Description: "wGaSjpEvAm", MinBandwidth: 0.9034219, DivertType: 36, OpType: 333, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 324, ConfigArgs: "YnHNCizbOR", ProductName: "CzvyOvRTbe", ProductCode: "BKUuPBEUJx", ErrorInfo: "JMzBYJSWfI"}
var socGroupTicketIDs = []int{}

// Create SocGroupTicket test case
// 创建
func TestCreateSocGroupTicket(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "POST", BodyData: testSocGroupTicket, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	socGroupTicketIDs = append(socGroupTicketIDs, getDataMapId(result))
}

// CreateBulk SocGroupTicket test case
// 批量创建
func TestCreateBulkSocGroupTicket(t *testing.T) {
	remark1 := "NYFACxpZtS"
	bulkData1 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "wUnbWiKlnn", Type: "RmEHEpxfAZ", Description: "aZhQbiuTVi", MinBandwidth: 0.3679356, DivertType: 325, OpType: 340, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 324, ConfigArgs: "GIheSIcSDJ", ProductName: "zHOwldfbAT", ProductCode: "xKnMhzPqtP", ErrorInfo: "dLDmKdOOvq"}
	remark2 := "mWeFKZKcXf"
	bulkData2 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "QGJQALBzau", Type: "nXBYThhgcv", Description: "eLPIXNfipo", MinBandwidth: 0.03303192, DivertType: 376, OpType: 37, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 335, ConfigArgs: "ZmgnPcHOUL", ProductName: "rRHdONFRZl", ProductCode: "cwsqMAkibt", ErrorInfo: "XKTIweQlSR"}
	remark3 := "yPdSftxUim"
	bulkData3 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "sOWvRbFVfR", Type: "NfrTTlXpxf", Description: "NtFAicIKIj", MinBandwidth: 0.18723436, DivertType: 319, OpType: 33, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 386, ConfigArgs: "RwjUepHccP", ProductName: "cYFjaLPgQV", ProductCode: "kTUtGdTgbY", ErrorInfo: "QvCpEARvck"}
	remark4 := "kZUfkkeilP"
	bulkData4 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "kqSKKMXrKI", Type: "XyISNsxceG", Description: "bziTUgapSK", MinBandwidth: 0.24749172, DivertType: 350, OpType: 314, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 315, ConfigArgs: "RJZIDAkbRt", ProductName: "QrzGIUjCoY", ProductCode: "jlrbvGlPWV", ErrorInfo: "aimDUDqXGR"}
	remark5 := "oMIdJqHOpL"
	bulkData5 := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "VEGuBKpLvE", Type: "jghTnaHgCr", Description: "OSlfcwFpob", MinBandwidth: 0.8719464, DivertType: 385, OpType: 385, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 367, ConfigArgs: "TNdtEvcRPE", ProductName: "eZSjHKdAYq", ProductCode: "bqhsExkbvM", ErrorInfo: "MOqBltrlsg"}
	bulkDatas := [...]ent.SocGroupTicket{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: socGroupTicketApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		socGroupTicketIDs = append(socGroupTicketIDs, getDataMapId(v))
	}
}

// Query SocGroupTicket test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySocGroupTicket(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SocGroupTicket test case
// 根据 ID 查询
func TestQueryByIDSocGroupTicket(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "GET", UrlData: strconv.Itoa(socGroupTicketIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SocGroupTicket not exist test case
// 根据 ID 查询
func TestQueryByIDSocGroupTicketNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SocGroupTicket by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySocGroupTicketByName(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "GET", UrlData: socgroupticket.FieldName + "=" + testSocGroupTicket.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SocGroupTicket search by Name test case
// 分页搜索
func TestQuerySearchSocGroupTicketName(t *testing.T) {
	successExpectedResult.ResponseData = testSocGroupTicket.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "GET", UrlData: "search=" + testSocGroupTicket.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SocGroupTicket test case
// 根据 ID 修改
func TestUpdateByIDSocGroupTicket(t *testing.T) {
	remark12 := "nbNcvrmAUH"
	updateData := &ent.SocGroupTicket{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Name: "xJNdJuOpTE", Type: "iAYmZXDCkI", Description: "BHYGczUJxg", MinBandwidth: 0.45922306, DivertType: 398, OpType: 312, OpTime: time.Now().AddDate(0, 0, -1), ConfigType: 349, ConfigArgs: "fLucAYqyvk", ProductName: "GgWMDOstWq", ProductCode: "SmXnfUAXks", ErrorInfo: "qAZlRrEMeg"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "PUT", UrlData: strconv.Itoa(socGroupTicketIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SocGroupTicket test case
// 根据 ID 删除
func TestDeleteByIDSocGroupTicket(t *testing.T) {
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(socGroupTicketIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SocGroupTicket not exist test case
// 根据 ID 删除
func TestDeleteByIDSocGroupTicketNoExist(t *testing.T) {
	testCase := &CaseRule{Api: socGroupTicketApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SocGroupTicket test case
// 根据 IDs 批量删除
func TestDeleteBulkSocGroupTicket(t *testing.T) {
	testCase := &CaseRule{Api: socGroupTicketApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: socGroupTicketIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
