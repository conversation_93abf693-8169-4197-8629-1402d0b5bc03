package test

import (
	"meta/app/ent"
	"meta/app/ent/strategy"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var strategyApi = baseApi + "/strategy"
var remark11 = "FGoAGvsjZy"
var testStrategy = &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, Name: "azwhzmJuiG", Type: "gibPZTRUWw", Bps: 312, Pps: 369, BpsCount: 357, PpsCount: 399}
var strategyIDs = []int{}

// Create Strategy test case
// 创建
func TestCreateStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "POST", BodyData: testStrategy, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	strategyIDs = append(strategyIDs, getDataMapId(result))
}

// CreateBulk Strategy test case
// 批量创建
func TestCreateBulkStrategy(t *testing.T) {
	remark1 := "bHtkzZwzqU"
	bulkData1 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "MmjjPkKZpb", Type: "jFKDWEMIDv", Bps: 372, Pps: 361, BpsCount: 31, PpsCount: 340}
	remark2 := "vItBWjxPGH"
	bulkData2 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "klgtEpztdc", Type: "wcQhmDiVCy", Bps: 374, Pps: 343, BpsCount: 377, PpsCount: 399}
	remark3 := "XwfQpnOaDY"
	bulkData3 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "ubgTkwcokT", Type: "FteZikperE", Bps: 363, Pps: 399, BpsCount: 388, PpsCount: 33}
	remark4 := "OCMwSqorrO"
	bulkData4 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "bTPDOMutdx", Type: "MWRhqGMmBo", Bps: 359, Pps: 372, BpsCount: 327, PpsCount: 358}
	remark5 := "uwWLVgofbz"
	bulkData5 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "RsaYqEQhgY", Type: "vLHUsGSeag", Bps: 346, Pps: 394, BpsCount: 331, PpsCount: 352}
	bulkDatas := [...]ent.Strategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: strategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		strategyIDs = append(strategyIDs, getDataMapId(v))
	}
}

// Query Strategy test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Strategy test case
// 根据 ID 查询
func TestQueryByIDStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Strategy not exist test case
// 根据 ID 查询
func TestQueryByIDStrategyNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Strategy by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategyByName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strategy.FieldName + "=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Strategy search by Name test case
// 分页搜索
func TestQuerySearchStrategyName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "search=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Strategy test case
// 根据 ID 修改
func TestUpdateByIDStrategy(t *testing.T) {
	remark11 := "IJFVbyogkv"
	updateData := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, Name: "fwVSNDCrue", Type: "YvzdgmFZTc", Bps: 362, Pps: 354, BpsCount: 330, PpsCount: 373}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(strategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Strategy test case
// 根据 ID 删除
func TestDeleteByIDStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Strategy not exist test case
// 根据 ID 删除
func TestDeleteByIDStrategyNoExist(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Strategy test case
// 根据 IDs 批量删除
func TestDeleteBulkStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: strategyIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
