package test

import (
	"meta/app/ent"
	"meta/app/ent/spectrumalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var spectrumAlertApi = baseApi + "/spectrumalert"
var remark17 = "NxqcvnJmSo"
var testSpectrumAlert = &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark17, IP: "aGjLQMqyHj", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "JJnycZWaEC", Source: "cjQWtAEdib", MaxPps: 398, MaxBps: 344}
var spectrumAlertIDs = []int{}

// Create SpectrumAlert test case
// 创建
func TestCreateSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "POST", BodyData: testSpectrumAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	spectrumAlertIDs = append(spectrumAlertIDs, getDataMapId(result))
}

// CreateBulk SpectrumAlert test case
// 批量创建
func TestCreateBulkSpectrumAlert(t *testing.T) {
	remark1 := "eRheuFnYPf"
	bulkData1 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, IP: "ApxGQjtaVu", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "qieUrdyscx", Source: "ITpyuqsKuc", MaxPps: 354, MaxBps: 326}
	remark2 := "ngopontVqI"
	bulkData2 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, IP: "sqJNneUHVI", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "QEXoHPSwEO", Source: "rNDtwpOpWo", MaxPps: 333, MaxBps: 312}
	remark3 := "EfyEPGSlxy"
	bulkData3 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, IP: "nMrmqkmhfF", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "JuBJUjymSE", Source: "mxhVCEfuhR", MaxPps: 338, MaxBps: 396}
	remark4 := "azvwPSsSDu"
	bulkData4 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, IP: "SQhSlYkgTe", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "kmPyCHnhNi", Source: "QKupnpnCCG", MaxPps: 327, MaxBps: 312}
	remark5 := "xJuSfDHbrc"
	bulkData5 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, IP: "ThxOOJTxXv", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "NgYOgQPIil", Source: "RfrBDFTEnE", MaxPps: 399, MaxBps: 351}
	bulkDatas := [...]ent.SpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.IP
	testCase := &CaseRule{Api: spectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		spectrumAlertIDs = append(spectrumAlertIDs, getDataMapId(v))
	}
}

// Query SpectrumAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SpectrumAlert test case
// 根据 ID 查询
func TestQueryByIDSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(spectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SpectrumAlert not exist test case
// 根据 ID 查询
func TestQueryByIDSpectrumAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SpectrumAlert by IP test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumAlertByIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: spectrumalert.FieldIP + "=" + testSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SpectrumAlert search by IP test case
// 分页搜索
func TestQuerySearchSpectrumAlertIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "search=" + testSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SpectrumAlert test case
// 根据 ID 修改
func TestUpdateByIDSpectrumAlert(t *testing.T) {
	remark17 := "fuIJHMcbzd"
	updateData := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark17, IP: "JRuEnbVarH", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "ZmfrETxgYM", Source: "AjzjCTqGfD", MaxPps: 343, MaxBps: 318}
	successExpectedResult.ResponseData = updateData.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(spectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SpectrumAlert test case
// 根据 ID 删除
func TestDeleteByIDSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(spectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SpectrumAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDSpectrumAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SpectrumAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: spectrumAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
