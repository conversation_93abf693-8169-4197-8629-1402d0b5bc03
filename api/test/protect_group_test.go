package test

import (
	"meta/app/ent"
	"meta/app/ent/protectgroup"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var protectGroupApi = baseApi + "/protectgroup"
var remark11 = "tuuuOnnDqp"
var testProtectGroup = &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, GroupName: "FvjRJjvgIK", Type: 326, ExpandIP: "GxKIeMBxOR"}
var protectGroupIDs = []int{}

// Create ProtectGroup test case
// 创建
func TestCreateProtectGroup(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "POST", BodyData: testProtectGroup, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	protectGroupIDs = append(protectGroupIDs, getDataMapId(result))
}

// CreateBulk ProtectGroup test case
// 批量创建
func TestCreateBulkProtectGroup(t *testing.T) {
	remark1 := "igjJcYLhwe"
	bulkData1 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, GroupName: "RIsJUFMaZi", Type: 357, ExpandIP: "SNheRsdgGm"}
	remark2 := "CRQPGtjqaE"
	bulkData2 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, GroupName: "RucflTxZnQ", Type: 389, ExpandIP: "oIoUdmCCGI"}
	remark3 := "kFvqBcDWUM"
	bulkData3 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, GroupName: "ngamWetDzF", Type: 332, ExpandIP: "eGQenQnrzv"}
	remark4 := "jHfDZJGkrc"
	bulkData4 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, GroupName: "GWIGHXkvRB", Type: 380, ExpandIP: "MXriCkKcmA"}
	remark5 := "FNPqYNIRBY"
	bulkData5 := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, GroupName: "QHbZJQkwEJ", Type: 324, ExpandIP: "PJKkVzNXNE"}
	bulkDatas := [...]ent.ProtectGroup{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.GroupName
	testCase := &CaseRule{Api: protectGroupApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		protectGroupIDs = append(protectGroupIDs, getDataMapId(v))
	}
}

// Query ProtectGroup test case
// 根据指定字段、时间范围查询或搜索
func TestQueryProtectGroup(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID ProtectGroup test case
// 根据 ID 查询
func TestQueryByIDProtectGroup(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "GET", UrlData: strconv.Itoa(protectGroupIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID ProtectGroup not exist test case
// 根据 ID 查询
func TestQueryByIDProtectGroupNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query ProtectGroup by GroupName test case
// 根据指定字段、时间范围查询或搜索
func TestQueryProtectGroupByGroupName(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "GET", UrlData: protectgroup.FieldGroupName + "=" + testProtectGroup.GroupName, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch ProtectGroup search by GroupName test case
// 分页搜索
func TestQuerySearchProtectGroupGroupName(t *testing.T) {
	successExpectedResult.ResponseData = testProtectGroup.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "GET", UrlData: "search=" + testProtectGroup.GroupName, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID ProtectGroup test case
// 根据 ID 修改
func TestUpdateByIDProtectGroup(t *testing.T) {
	remark11 := "ercACHkBVu"
	updateData := &ent.ProtectGroup{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, GroupName: "sgpMCpzxrp", Type: 316, ExpandIP: "mKpDneqBJp"}
	successExpectedResult.ResponseData = updateData.GroupName
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "PUT", UrlData: strconv.Itoa(protectGroupIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID ProtectGroup test case
// 根据 ID 删除
func TestDeleteByIDProtectGroup(t *testing.T) {
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(protectGroupIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID ProtectGroup not exist test case
// 根据 ID 删除
func TestDeleteByIDProtectGroupNoExist(t *testing.T) {
	testCase := &CaseRule{Api: protectGroupApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk ProtectGroup test case
// 根据 IDs 批量删除
func TestDeleteBulkProtectGroup(t *testing.T) {
	testCase := &CaseRule{Api: protectGroupApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: protectGroupIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
